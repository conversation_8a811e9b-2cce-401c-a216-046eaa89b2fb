const http = require('http');
const fs = require('fs');

function countStudents(path) {
  return new Promise((resolve, reject) => {
    fs.readFile(path, 'utf8', (err, data) => {
      if (err) {
        reject(new Error('Cannot load the database'));
        return;
      }

      const lines = data.split(/\r?\n/).filter((line) => line.trim() !== '');
      if (lines.length <= 1) {
        resolve('Number of students: 0');
        return;
      }

      const rows = lines.slice(1).map((line) => line.split(',').map((s) => s.trim()));
      let output = `Number of students: ${rows.length}\n`;

      const groups = {};
      rows.forEach(([firstname, , , field]) => {
        if (!firstname || !field) return;
        if (!groups[field]) groups[field] = [];
        groups[field].push(firstname);
      });

      Object.keys(groups).sort().forEach((field) => {
        const list = groups[field].join(', ');
        output += `Number of students in ${field}: ${groups[field].length}. List: ${list}\n`;
      });

      resolve(output.trim());
    });
  });
}

const app = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'text/plain');

  if (req.url === '/') {
    res.end('Hello Holberton School!');
    return;
  }

  if (req.url === '/students') {
    res.write('This is the list of our students\n');
    countStudents(process.argv[2])
      .then((output) => {
        res.end(output);
      })
      .catch(() => {
        res.end('Cannot load the database');
      });
    return;
  }

  res.statusCode = 404;
  res.end('Not found');
});

app.listen(1245);
module.exports = app;
